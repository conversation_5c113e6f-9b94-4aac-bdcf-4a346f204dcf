# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: vm_management.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'vm_management.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13vm_management.proto\x12\x0cturdparty.vm\"\xe8\x01\n\x0f\x43reateVMRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08template\x18\x02 \x01(\t\x12\x11\n\tmemory_mb\x18\x03 \x01(\x05\x12\x0c\n\x04\x63pus\x18\x04 \x01(\x05\x12\x0f\n\x07\x64isk_gb\x18\x05 \x01(\x05\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12=\n\x08metadata\x18\x07 \x03(\x0b\x32+.turdparty.vm.CreateVMRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"y\n\x10\x43reateVMResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05vm_id\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\r\n\x05\x65rror\x18\x04 \x01(\t\x12%\n\x07vm_info\x18\x05 \x01(\x0b\x32\x14.turdparty.vm.VMInfo\"#\n\x12GetVMStatusRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\"\\\n\x13GetVMStatusResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12%\n\x07vm_info\x18\x02 \x01(\x0b\x32\x14.turdparty.vm.VMInfo\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"\x1f\n\x0eStartVMRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\"B\n\x0fStartVMResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"-\n\rStopVMRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12\r\n\x05\x66orce\x18\x02 \x01(\x08\"A\n\x0eStopVMResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"0\n\x10\x44\x65stroyVMRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12\r\n\x05\x66orce\x18\x02 \x01(\x08\"D\n\x11\x44\x65stroyVMResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"\xf2\x01\n\x11InjectFileRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12\x11\n\tfile_path\x18\x02 \x01(\t\x12\x13\n\x0btarget_path\x18\x03 \x01(\t\x12\x13\n\x0bpermissions\x18\x04 \x01(\t\x12\x1f\n\x17\x65xecute_after_injection\x18\x05 \x01(\x08\x12?\n\x08metadata\x18\x06 \x03(\x0b\x32-.turdparty.vm.InjectFileRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x90\x01\n\x12InjectFileResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x14\n\x0cinjection_id\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\r\n\x05\x65rror\x18\x04 \x01(\t\x12\x33\n\x0einjection_info\x18\x05 \x01(\x0b\x32\x1b.turdparty.vm.InjectionInfo\"\xf8\x01\n\x15\x45xecuteCommandRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63ommand\x18\x02 \x01(\t\x12\x0c\n\x04\x61rgs\x18\x03 \x03(\t\x12\x19\n\x11working_directory\x18\x04 \x01(\t\x12I\n\x0b\x65nvironment\x18\x05 \x03(\x0b\x32\x34.turdparty.vm.ExecuteCommandRequest.EnvironmentEntry\x12\x17\n\x0ftimeout_seconds\x18\x06 \x01(\x05\x1a\x32\n\x10\x45nvironmentEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\\\n\x16\x45xecuteCommandResponse\x12\x0e\n\x06output\x18\x01 \x01(\t\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x11\n\texit_code\x18\x03 \x01(\x05\x12\x10\n\x08\x66inished\x18\x04 \x01(\x08\"$\n\x13GetVMMetricsRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\"`\n\x14GetVMMetricsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12(\n\x07metrics\x18\x02 \x01(\x0b\x32\x17.turdparty.vm.VMMetrics\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"A\n\x16StreamVMMetricsRequest\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12\x18\n\x10interval_seconds\x18\x02 \x01(\x05\"]\n\x0fVMMetricsUpdate\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12(\n\x07metrics\x18\x02 \x01(\x0b\x32\x17.turdparty.vm.VMMetrics\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\"\xdb\x02\n\x06VMInfo\x12\r\n\x05vm_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08template\x18\x03 \x01(\t\x12&\n\x06status\x18\x04 \x01(\x0e\x32\x16.turdparty.vm.VMStatus\x12\x12\n\nip_address\x18\x05 \x01(\t\x12\x10\n\x08ssh_port\x18\x06 \x01(\x05\x12\x11\n\tmemory_mb\x18\x07 \x01(\x05\x12\x0c\n\x04\x63pus\x18\x08 \x01(\x05\x12\x0f\n\x07\x64isk_gb\x18\t \x01(\x05\x12\x12\n\ncreated_at\x18\n \x01(\t\x12\x12\n\nstarted_at\x18\x0b \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0c \x01(\t\x12\x34\n\x08metadata\x18\r \x03(\x0b\x32\".turdparty.vm.VMInfo.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xab\x01\n\rInjectionInfo\x12\x14\n\x0cinjection_id\x18\x01 \x01(\t\x12\r\n\x05vm_id\x18\x02 \x01(\t\x12\x11\n\tfile_path\x18\x03 \x01(\t\x12\x13\n\x0btarget_path\x18\x04 \x01(\t\x12\x13\n\x0bpermissions\x18\x05 \x01(\t\x12\x10\n\x08\x65xecuted\x18\x06 \x01(\x08\x12\x13\n\x0binjected_at\x18\x07 \x01(\t\x12\x11\n\tfile_size\x18\x08 \x01(\x03\"\xa9\x02\n\tVMMetrics\x12\x13\n\x0b\x63pu_percent\x18\x01 \x01(\x01\x12\x16\n\x0ememory_percent\x18\x02 \x01(\x01\x12\x16\n\x0ememory_used_mb\x18\x03 \x01(\x03\x12\x17\n\x0fmemory_total_mb\x18\x04 \x01(\x03\x12\x14\n\x0c\x64isk_percent\x18\x05 \x01(\x01\x12\x14\n\x0c\x64isk_used_gb\x18\x06 \x01(\x03\x12\x15\n\rdisk_total_gb\x18\x07 \x01(\x03\x12\x1a\n\x12network_bytes_sent\x18\x08 \x01(\x03\x12\x1a\n\x12network_bytes_recv\x18\t \x01(\x03\x12\x15\n\rprocess_count\x18\n \x01(\x05\x12,\n\tprocesses\x18\x0b \x03(\x0b\x32\x19.turdparty.vm.ProcessInfo\"\x8a\x01\n\x0bProcessInfo\x12\x0b\n\x03pid\x18\x01 \x01(\x05\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0c\x63ommand_line\x18\x03 \x01(\t\x12\x13\n\x0b\x63pu_percent\x18\x04 \x01(\x01\x12\x11\n\tmemory_mb\x18\x05 \x01(\x03\x12\x0e\n\x06status\x18\x06 \x01(\t\x12\x12\n\nstarted_at\x18\x07 \x01(\t*\xf8\x01\n\x08VMStatus\x12\x15\n\x11VM_STATUS_UNKNOWN\x10\x00\x12\x16\n\x12VM_STATUS_CREATING\x10\x01\x12\x16\n\x12VM_STATUS_STARTING\x10\x02\x12\x15\n\x11VM_STATUS_RUNNING\x10\x03\x12\x16\n\x12VM_STATUS_STOPPING\x10\x04\x12\x15\n\x11VM_STATUS_STOPPED\x10\x05\x12\x18\n\x14VM_STATUS_DESTROYING\x10\x06\x12\x17\n\x13VM_STATUS_DESTROYED\x10\x07\x12\x13\n\x0fVM_STATUS_ERROR\x10\x08\x12\x17\n\x13VM_STATUS_SUSPENDED\x10\t2\xe9\x05\n\x0cVMManagement\x12I\n\x08\x43reateVM\x12\x1d.turdparty.vm.CreateVMRequest\x1a\x1e.turdparty.vm.CreateVMResponse\x12R\n\x0bGetVMStatus\x12 .turdparty.vm.GetVMStatusRequest\x1a!.turdparty.vm.GetVMStatusResponse\x12\x46\n\x07StartVM\x12\x1c.turdparty.vm.StartVMRequest\x1a\x1d.turdparty.vm.StartVMResponse\x12\x43\n\x06StopVM\x12\x1b.turdparty.vm.StopVMRequest\x1a\x1c.turdparty.vm.StopVMResponse\x12L\n\tDestroyVM\x12\x1e.turdparty.vm.DestroyVMRequest\x1a\x1f.turdparty.vm.DestroyVMResponse\x12O\n\nInjectFile\x12\x1f.turdparty.vm.InjectFileRequest\x1a .turdparty.vm.InjectFileResponse\x12]\n\x0e\x45xecuteCommand\x12#.turdparty.vm.ExecuteCommandRequest\x1a$.turdparty.vm.ExecuteCommandResponse0\x01\x12U\n\x0cGetVMMetrics\x12!.turdparty.vm.GetVMMetricsRequest\x1a\".turdparty.vm.GetVMMetricsResponse\x12X\n\x0fStreamVMMetrics\x12$.turdparty.vm.StreamVMMetricsRequest\x1a\x1d.turdparty.vm.VMMetricsUpdate0\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'vm_management_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_CREATEVMREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_CREATEVMREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_INJECTFILEREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_INJECTFILEREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_EXECUTECOMMANDREQUEST_ENVIRONMENTENTRY']._loaded_options = None
  _globals['_EXECUTECOMMANDREQUEST_ENVIRONMENTENTRY']._serialized_options = b'8\001'
  _globals['_VMINFO_METADATAENTRY']._loaded_options = None
  _globals['_VMINFO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_VMSTATUS']._serialized_start=2862
  _globals['_VMSTATUS']._serialized_end=3110
  _globals['_CREATEVMREQUEST']._serialized_start=38
  _globals['_CREATEVMREQUEST']._serialized_end=270
  _globals['_CREATEVMREQUEST_METADATAENTRY']._serialized_start=223
  _globals['_CREATEVMREQUEST_METADATAENTRY']._serialized_end=270
  _globals['_CREATEVMRESPONSE']._serialized_start=272
  _globals['_CREATEVMRESPONSE']._serialized_end=393
  _globals['_GETVMSTATUSREQUEST']._serialized_start=395
  _globals['_GETVMSTATUSREQUEST']._serialized_end=430
  _globals['_GETVMSTATUSRESPONSE']._serialized_start=432
  _globals['_GETVMSTATUSRESPONSE']._serialized_end=524
  _globals['_STARTVMREQUEST']._serialized_start=526
  _globals['_STARTVMREQUEST']._serialized_end=557
  _globals['_STARTVMRESPONSE']._serialized_start=559
  _globals['_STARTVMRESPONSE']._serialized_end=625
  _globals['_STOPVMREQUEST']._serialized_start=627
  _globals['_STOPVMREQUEST']._serialized_end=672
  _globals['_STOPVMRESPONSE']._serialized_start=674
  _globals['_STOPVMRESPONSE']._serialized_end=739
  _globals['_DESTROYVMREQUEST']._serialized_start=741
  _globals['_DESTROYVMREQUEST']._serialized_end=789
  _globals['_DESTROYVMRESPONSE']._serialized_start=791
  _globals['_DESTROYVMRESPONSE']._serialized_end=859
  _globals['_INJECTFILEREQUEST']._serialized_start=862
  _globals['_INJECTFILEREQUEST']._serialized_end=1104
  _globals['_INJECTFILEREQUEST_METADATAENTRY']._serialized_start=223
  _globals['_INJECTFILEREQUEST_METADATAENTRY']._serialized_end=270
  _globals['_INJECTFILERESPONSE']._serialized_start=1107
  _globals['_INJECTFILERESPONSE']._serialized_end=1251
  _globals['_EXECUTECOMMANDREQUEST']._serialized_start=1254
  _globals['_EXECUTECOMMANDREQUEST']._serialized_end=1502
  _globals['_EXECUTECOMMANDREQUEST_ENVIRONMENTENTRY']._serialized_start=1452
  _globals['_EXECUTECOMMANDREQUEST_ENVIRONMENTENTRY']._serialized_end=1502
  _globals['_EXECUTECOMMANDRESPONSE']._serialized_start=1504
  _globals['_EXECUTECOMMANDRESPONSE']._serialized_end=1596
  _globals['_GETVMMETRICSREQUEST']._serialized_start=1598
  _globals['_GETVMMETRICSREQUEST']._serialized_end=1634
  _globals['_GETVMMETRICSRESPONSE']._serialized_start=1636
  _globals['_GETVMMETRICSRESPONSE']._serialized_end=1732
  _globals['_STREAMVMMETRICSREQUEST']._serialized_start=1734
  _globals['_STREAMVMMETRICSREQUEST']._serialized_end=1799
  _globals['_VMMETRICSUPDATE']._serialized_start=1801
  _globals['_VMMETRICSUPDATE']._serialized_end=1894
  _globals['_VMINFO']._serialized_start=1897
  _globals['_VMINFO']._serialized_end=2244
  _globals['_VMINFO_METADATAENTRY']._serialized_start=223
  _globals['_VMINFO_METADATAENTRY']._serialized_end=270
  _globals['_INJECTIONINFO']._serialized_start=2247
  _globals['_INJECTIONINFO']._serialized_end=2418
  _globals['_VMMETRICS']._serialized_start=2421
  _globals['_VMMETRICS']._serialized_end=2718
  _globals['_PROCESSINFO']._serialized_start=2721
  _globals['_PROCESSINFO']._serialized_end=2859
  _globals['_VMMANAGEMENT']._serialized_start=3113
  _globals['_VMMANAGEMENT']._serialized_end=3858
# @@protoc_insertion_point(module_scope)
