#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 VirtualBox gRPC Host Integration Tests

Tests VirtualBox VM creation and gRPC communication on the host system.
This test validates the complete Vagrant + VirtualBox + gRPC workflow
for Windows VM analysis.

Requirements:
- VirtualBox installed on host
- Vagrant with VirtualBox provider
- gRPC service running on port 40000
- 10Baht/windows10-turdparty box available
"""

import asyncio
import json
import os
import subprocess
import time
import uuid
from pathlib import Path

import requests
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Test configuration
API_BASE_URL = "http://localhost:8000"
GRPC_PORT = 40000
TEST_TIMEOUT = 1800  # 30 minutes for VM operations
VM_BOOT_TIMEOUT = 600  # 10 minutes for VM boot


class VirtualBoxGRPCTester:
    """VirtualBox gRPC integration tester."""

    def __init__(self):
        self.console = Console()
        self.test_vms = []
        self.test_files = []
        
        # Test file for injection
        self.test_file_content = b"TurdParty VirtualBox gRPC Test File"
        self.test_file_name = "virtualbox-grpc-test.exe"
        self.test_file_id = None

    def print_header(self):
        """Print test header."""
        header_panel = Panel(
            "🖥️ [bold magenta]💩🎉TurdParty🎉💩 VirtualBox gRPC Tests[/bold magenta] 🖥️\n\n"
            "🎯 Target: VirtualBox + Vagrant + gRPC Integration\n"
            "🔗 gRPC Port: localhost:40000\n"
            "🖥️ Provider: VirtualBox with Windows 10 VM\n"
            "📊 Pipeline: VM Create → gRPC Connect → File Inject → Execute",
            title="VirtualBox gRPC Host Integration Tests",
            border_style="magenta",
        )
        self.console.print(header_panel)
        self.console.print()

    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are available."""
        self.console.print("[bold blue]🔍 Checking Prerequisites...[/bold blue]")
        
        checks = []
        
        # Check VirtualBox
        try:
            result = subprocess.run(
                ["VBoxManage", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                vbox_version = result.stdout.strip()
                self.console.print(f"[green]✅ VirtualBox: {vbox_version}[/green]")
                checks.append(True)
            else:
                self.console.print("[red]❌ VirtualBox not found[/red]")
                checks.append(False)
        except Exception as e:
            self.console.print(f"[red]❌ VirtualBox check failed: {e}[/red]")
            checks.append(False)

        # Check Vagrant
        try:
            result = subprocess.run(
                ["vagrant", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                vagrant_version = result.stdout.strip()
                self.console.print(f"[green]✅ Vagrant: {vagrant_version}[/green]")
                checks.append(True)
            else:
                self.console.print("[red]❌ Vagrant not found[/red]")
                checks.append(False)
        except Exception as e:
            self.console.print(f"[red]❌ Vagrant check failed: {e}[/red]")
            checks.append(False)

        # Check gRPC port
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', GRPC_PORT))
            sock.close()
            
            if result == 0:
                self.console.print(f"[green]✅ gRPC Port {GRPC_PORT}: Available[/green]")
                checks.append(True)
            else:
                self.console.print(f"[red]❌ gRPC Port {GRPC_PORT}: Not accessible[/red]")
                checks.append(False)
        except Exception as e:
            self.console.print(f"[red]❌ gRPC port check failed: {e}[/red]")
            checks.append(False)

        # Check API availability
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                self.console.print(f"[green]✅ TurdParty API: Available[/green]")
                checks.append(True)
            else:
                self.console.print(f"[red]❌ TurdParty API: Not available[/red]")
                checks.append(False)
        except Exception as e:
            self.console.print(f"[red]❌ API check failed: {e}[/red]")
            checks.append(False)

        all_good = all(checks)
        if all_good:
            self.console.print("[green]✅ All prerequisites satisfied[/green]")
        else:
            self.console.print("[red]❌ Some prerequisites missing[/red]")
        
        return all_good

    def setup_test_file(self) -> bool:
        """Set up test file for injection."""
        self.console.print("[bold blue]📁 Setting up test file...[/bold blue]")
        
        try:
            # Upload test file
            files = {
                "file": (self.test_file_name, self.test_file_content, "application/octet-stream")
            }
            data = {
                "description": "VirtualBox gRPC integration test file",
                "source": "test_suite",
            }
            
            response = requests.post(
                f"{API_BASE_URL}/api/v1/files/upload",
                files=files,
                data=data,
                timeout=60
            )
            
            if response.status_code in [200, 201]:
                upload_info = response.json()
                self.test_file_id = upload_info.get("file_id") or upload_info.get("uuid")
                self.test_files.append(self.test_file_id)
                
                self.console.print(f"[green]✅ Test file uploaded: {self.test_file_id}[/green]")
                return True
            else:
                self.console.print(f"[red]❌ File upload failed: {response.status_code}[/red]")
                return False
                
        except Exception as e:
            self.console.print(f"[red]❌ File setup failed: {e}[/red]")
            return False

    def test_virtualbox_vm_creation(self) -> bool:
        """Test VirtualBox VM creation via Vagrant."""
        self.console.print("[bold blue]🖥️ Testing VirtualBox VM Creation...[/bold blue]")
        
        try:
            # Create VM configuration with explicit VirtualBox provider
            vm_config = {
                "name": f"test-vbox-grpc-{uuid.uuid4().hex[:8]}",
                "template": "10Baht/windows10-turdparty",  # Use TurdParty Windows template
                "vm_type": "vagrant",
                "provider": "virtualbox",  # Explicitly specify VirtualBox provider
                "memory_mb": 4096,
                "cpus": 2,
                "disk_gb": 40,
                "description": "VirtualBox gRPC integration test VM (Windows 2019)",
                "tags": ["virtualbox", "grpc", "integration-test", "windows2019"],
            }
            
            self.console.print(f"[cyan]📋 Creating VM: {vm_config['name']}[/cyan]")
            
            response = requests.post(
                f"{API_BASE_URL}/api/v1/vms/",
                json=vm_config,
                timeout=60
            )
            
            if response.status_code in [200, 201]:
                vm_data = response.json()
                vm_id = vm_data.get("vm_id") or vm_data.get("id")
                self.test_vms.append(vm_id)
                
                self.console.print(f"[green]✅ VM created: {vm_id}[/green]")
                
                # Wait for VM to be ready
                return self.wait_for_vm_ready(vm_id)
            else:
                self.console.print(f"[red]❌ VM creation failed: {response.status_code} - {response.text}[/red]")
                return False
                
        except Exception as e:
            self.console.print(f"[red]❌ VM creation error: {e}[/red]")
            return False

    def wait_for_vm_ready(self, vm_id: str) -> bool:
        """Wait for VM to be ready."""
        self.console.print(f"[yellow]⏳ Waiting for VM {vm_id} to be ready...[/yellow]")
        
        start_time = time.time()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("VM provisioning...", total=None)
            
            while time.time() - start_time < VM_BOOT_TIMEOUT:
                try:
                    response = requests.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}", timeout=30)
                    
                    if response.status_code == 200:
                        vm_status = response.json()
                        status = vm_status.get("status", "unknown")
                        
                        progress.update(task, description=f"VM status: {status}")
                        
                        if status == "running":
                            progress.update(task, description="✅ VM ready")
                            self.console.print("[green]✅ VM is ready for testing[/green]")
                            return True
                        elif status in ["failed", "error"]:
                            progress.update(task, description="❌ VM failed")
                            self.console.print(f"[red]❌ VM failed: {vm_status.get('error_message', 'Unknown error')}[/red]")
                            return False
                            
                except Exception as e:
                    self.console.print(f"[yellow]⚠️ Status check error: {e}[/yellow]")
                
                time.sleep(15)
            
            progress.update(task, description="❌ VM timeout")
            self.console.print("[red]❌ VM readiness timeout[/red]")
            return False

    def test_grpc_file_injection(self, vm_id: str) -> bool:
        """Test file injection via gRPC."""
        self.console.print("[bold blue]💉 Testing gRPC File Injection...[/bold blue]")
        
        try:
            injection_config = {
                "file_id": self.test_file_id,
                "injection_path": "C:\\TurdParty\\virtualbox-grpc-test.exe",
                "execute_after_injection": True,
                "monitor": True,
                "permissions": "0755",
            }
            
            self.console.print(f"[cyan]📡 Injecting via gRPC on port {GRPC_PORT}[/cyan]")
            
            response = requests.post(
                f"{API_BASE_URL}/api/v1/vms/{vm_id}/inject",
                json=injection_config,
                timeout=120
            )
            
            if response.status_code in [200, 201]:
                injection_info = response.json()
                injection_id = injection_info.get("injection_id")
                
                self.console.print(f"[green]✅ File injection initiated: {injection_id}[/green]")
                self.console.print("[cyan]🔗 Using gRPC communication channel[/cyan]")
                
                # Wait for injection to complete
                return self.wait_for_injection_complete(injection_id)
            else:
                self.console.print(f"[red]❌ Injection failed: {response.status_code} - {response.text}[/red]")
                return False
                
        except Exception as e:
            self.console.print(f"[red]❌ Injection error: {e}[/red]")
            return False

    def wait_for_injection_complete(self, injection_id: str) -> bool:
        """Wait for injection to complete."""
        self.console.print(f"[yellow]⏳ Monitoring injection {injection_id}...[/yellow]")
        
        start_time = time.time()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("Processing injection...", total=None)
            
            while time.time() - start_time < 300:  # 5 minute timeout
                try:
                    response = requests.get(
                        f"{API_BASE_URL}/api/v1/file_injection/{injection_id}/status",
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        injection_status = response.json()
                        status = injection_status.get("status", "unknown")
                        
                        progress.update(task, description=f"Injection status: {status}")
                        
                        if status == "COMPLETED":
                            progress.update(task, description="✅ Injection completed")
                            self.console.print("[green]✅ gRPC injection completed successfully[/green]")
                            return True
                        elif status in ["FAILED", "ERROR"]:
                            progress.update(task, description="❌ Injection failed")
                            self.console.print(f"[red]❌ Injection failed: {injection_status}[/red]")
                            return False
                            
                except Exception as e:
                    self.console.print(f"[yellow]⚠️ Status check error: {e}[/yellow]")
                
                time.sleep(10)
            
            progress.update(task, description="❌ Injection timeout")
            self.console.print("[red]❌ Injection timeout[/red]")
            return False

    def cleanup_resources(self):
        """Clean up test resources."""
        self.console.print("[bold blue]🧹 Cleaning up test resources...[/bold blue]")
        
        # Clean up VMs
        for vm_id in self.test_vms:
            try:
                response = requests.delete(f"{API_BASE_URL}/api/v1/vms/{vm_id}", timeout=60)
                if response.status_code in [200, 204]:
                    self.console.print(f"[green]✅ Cleaned up VM: {vm_id}[/green]")
                else:
                    self.console.print(f"[yellow]⚠️ VM cleanup warning: {vm_id}[/yellow]")
            except Exception as e:
                self.console.print(f"[yellow]⚠️ VM cleanup error: {e}[/yellow]")
        
        # Clean up files
        for file_id in self.test_files:
            try:
                response = requests.delete(f"{API_BASE_URL}/api/v1/files/{file_id}", timeout=30)
                if response.status_code in [200, 204]:
                    self.console.print(f"[green]✅ Cleaned up file: {file_id}[/green]")
                else:
                    self.console.print(f"[yellow]⚠️ File cleanup warning: {file_id}[/yellow]")
            except Exception as e:
                self.console.print(f"[yellow]⚠️ File cleanup error: {e}[/yellow]")

    def run_complete_test_suite(self) -> bool:
        """Run the complete VirtualBox gRPC test suite."""
        start_time = time.time()
        
        self.print_header()
        
        try:
            # Check prerequisites
            if not self.check_prerequisites():
                return False
            
            # Setup test file
            if not self.setup_test_file():
                return False
            
            # Test VM creation
            if not self.test_virtualbox_vm_creation():
                return False
            
            # Test gRPC injection
            vm_id = self.test_vms[0] if self.test_vms else None
            if vm_id and not self.test_grpc_file_injection(vm_id):
                return False
            
            # Success summary
            total_time = time.time() - start_time
            
            success_panel = Panel(
                f"🎉 [bold green]VirtualBox gRPC Tests Completed Successfully![/bold green] 🎉\n\n"
                f"⏱️ Total Time: {total_time:.2f} seconds\n"
                f"🖥️ VM ID: {vm_id}\n"
                f"🔗 gRPC Port: {GRPC_PORT}\n"
                f"💉 File Injection: SUCCESS\n\n"
                f"✅ VirtualBox + Vagrant + gRPC integration verified!",
                title="🖥️ VirtualBox gRPC Tests Complete",
                border_style="green",
            )
            self.console.print(success_panel)
            
            return True
            
        except Exception as e:
            error_msg = f"Test suite failed: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return False
        finally:
            self.cleanup_resources()


def main():
    """Main function to run VirtualBox gRPC tests."""
    tester = VirtualBoxGRPCTester()
    
    try:
        success = tester.run_complete_test_suite()
        
        if success:
            print(f"\n🎉 VirtualBox gRPC integration tests passed!")
            return 0
        else:
            print(f"\n❌ VirtualBox gRPC integration tests failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        tester.cleanup_resources()
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        tester.cleanup_resources()
        return 1


if __name__ == "__main__":
    exit(main())
