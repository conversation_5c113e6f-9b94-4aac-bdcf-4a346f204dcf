#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Real Integration Test Configuration

This configuration file replaces mocked components with real VirtualBox VMs,
gRPC communication, and actual file injection workflows. Use this for
integration tests that require real VM operations.

Usage:
    pytest --conftest=tests/conftest_real_integration.py tests/integration/
"""

import asyncio
import os
import pytest
import tempfile
import uuid
from pathlib import Path
from typing import Dict, Any, Generator

import requests
from services.grpc.vm_client import VMGRPCClient


# Test configuration for real integration
REAL_INTEGRATION_CONFIG = {
    "api_base_url": "http://localhost:8000",
    "grpc_endpoint": "localhost:40000",
    "elasticsearch_url": "http://elasticsearch.turdparty.localhost:9200",
    "vm_template": "10Baht/windows10-turdparty",
    "vm_provider": "virtualbox",
    "test_timeout": 1800,  # 30 minutes
    "use_real_vms": True,
    "use_real_grpc": True,
    "use_real_file_injection": True,
    "generate_real_reports": True
}


@pytest.fixture(scope="session")
def real_integration_config():
    """Provide real integration test configuration."""
    return REAL_INTEGRATION_CONFIG


@pytest.fixture(scope="session")
def real_grpc_client():
    """Provide real gRPC client for VM operations."""
    client = VMGRPCClient(REAL_INTEGRATION_CONFIG["grpc_endpoint"])
    
    # Test connection
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        connected = loop.run_until_complete(client.connect())
        if not connected:
            pytest.skip("gRPC service not available for real integration tests")
        
        yield client
        
    finally:
        loop.run_until_complete(client.disconnect())
        loop.close()


@pytest.fixture
def real_test_vm(real_grpc_client, real_integration_config) -> Generator[Dict[str, Any], None, None]:
    """Create a real VirtualBox VM for testing."""
    vm_config = {
        "name": f"pytest-real-vm-{uuid.uuid4().hex[:8]}",
        "template": real_integration_config["vm_template"],
        "vm_type": "vagrant",
        "provider": real_integration_config["vm_provider"],
        "memory_mb": 4096,
        "cpus": 2,
        "disk_gb": 40,
        "description": "Real integration test VM",
        "tags": ["pytest", "real-integration", "virtualbox"],
        "metadata": {
            "test_type": "real_integration",
            "created_by": "pytest",
            "grpc_endpoint": real_integration_config["grpc_endpoint"]
        }
    }
    
    # Create VM via API
    response = requests.post(
        f"{real_integration_config['api_base_url']}/api/v1/vms/",
        json=vm_config,
        timeout=120
    )
    
    if response.status_code not in [200, 201]:
        pytest.skip(f"Failed to create real VM: {response.status_code}")
    
    vm_info = response.json()
    vm_id = vm_info.get("vm_id")
    
    # Wait for VM to be ready
    _wait_for_vm_ready(vm_id, real_integration_config)
    
    yield {
        "vm_id": vm_id,
        "vm_info": vm_info,
        "config": vm_config,
        "grpc_client": real_grpc_client
    }
    
    # Cleanup VM
    try:
        requests.delete(
            f"{real_integration_config['api_base_url']}/api/v1/vms/{vm_id}",
            timeout=60
        )
    except Exception as e:
        print(f"Warning: Failed to cleanup VM {vm_id}: {e}")


@pytest.fixture
def real_test_file(real_integration_config) -> Generator[Dict[str, Any], None, None]:
    """Create a real test file for injection."""
    test_content = b"""@echo off
echo TurdParty Real Integration Test > C:\\TurdParty\\real-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\real-test-result.txt
echo Test UUID: """ + str(uuid.uuid4()).encode() + b""" >> C:\\TurdParty\\real-test-result.txt
echo Creating test registry entries...
reg add "HKCU\\Software\\TurdParty\\RealTest" /v "TestRun" /t REG_SZ /d "%date%_%time%" /f
echo Creating test files...
echo Real test file 1 > C:\\TurdParty\\real-test-file-1.txt
echo Real test file 2 > C:\\TurdParty\\real-test-file-2.txt
mkdir C:\\TurdParty\\RealTestDirectory
echo Real directory test > C:\\TurdParty\\RealTestDirectory\\real-dir-test.txt
echo Real integration test completed successfully! >> C:\\TurdParty\\real-test-result.txt
"""
    
    filename = f"real-integration-test-{uuid.uuid4().hex[:8]}.bat"
    
    # Upload file via API
    files = {
        'file': (filename, test_content, 'application/octet-stream')
    }
    
    response = requests.post(
        f"{real_integration_config['api_base_url']}/api/v1/files/upload",
        files=files,
        timeout=60
    )
    
    if response.status_code not in [200, 201]:
        pytest.skip(f"Failed to upload real test file: {response.status_code}")
    
    file_info = response.json()
    file_id = file_info.get("file_id")
    
    yield {
        "file_id": file_id,
        "filename": filename,
        "content": test_content,
        "file_info": file_info
    }
    
    # Cleanup file
    try:
        requests.delete(
            f"{real_integration_config['api_base_url']}/api/v1/files/{file_id}",
            timeout=30
        )
    except Exception as e:
        print(f"Warning: Failed to cleanup file {file_id}: {e}")


@pytest.fixture
def real_file_injection_service(real_test_vm, real_test_file, real_integration_config):
    """Provide real file injection service using gRPC."""
    
    class RealFileInjectionService:
        def __init__(self, vm_data, file_data, config):
            self.vm_data = vm_data
            self.file_data = file_data
            self.config = config
            self.grpc_client = vm_data["grpc_client"]
        
        async def inject_file(self, target_path: str = None, execute: bool = True) -> Dict[str, Any]:
            """Inject file into real VM via gRPC."""
            if target_path is None:
                target_path = f"C:\\TurdParty\\{self.file_data['filename']}"
            
            # Use real gRPC injection
            result = await self.grpc_client.inject_file(
                vm_id=self.vm_data["vm_id"],
                file_path=f"/tmp/{self.file_data['filename']}",  # Temp path on host
                target_path=target_path,
                permissions="0755",
                execute=execute
            )
            
            return result
        
        async def execute_command(self, command: str) -> Dict[str, Any]:
            """Execute command in real VM via gRPC."""
            result = await self.grpc_client.execute_command(
                vm_id=self.vm_data["vm_id"],
                command=command,
                timeout_seconds=300
            )
            
            return result
        
        def get_vm_id(self) -> str:
            """Get VM ID."""
            return self.vm_data["vm_id"]
        
        def get_file_id(self) -> str:
            """Get file ID."""
            return self.file_data["file_id"]
    
    return RealFileInjectionService(real_test_vm, real_test_file, real_integration_config)


@pytest.fixture
def real_report_generator(real_integration_config):
    """Provide real report generator that uses actual VM execution data."""
    
    class RealReportGenerator:
        def __init__(self, config):
            self.config = config
        
        def generate_report(self, file_id: str) -> Dict[str, Any]:
            """Generate real report from VM execution data."""
            import subprocess
            import sys
            from pathlib import Path
            
            # Use the updated report generator script
            project_root = Path(__file__).parent.parent
            report_script = project_root / "scripts" / "generate-generic-report.py"
            
            result = subprocess.run([
                sys.executable, str(report_script), file_id
            ], capture_output=True, text=True, timeout=300, cwd=project_root)
            
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "file_id": file_id
            }
        
        def get_report_data(self, file_id: str) -> Dict[str, Any]:
            """Get report data via API."""
            response = requests.get(
                f"{self.config['api_base_url']}/api/v1/reports/binary/{file_id}",
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Failed to get report: {response.status_code}"}
    
    return RealReportGenerator(real_integration_config)


def _wait_for_vm_ready(vm_id: str, config: Dict[str, Any], timeout: int = 600):
    """Wait for VM to be ready."""
    import time
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(
                f"{config['api_base_url']}/api/v1/vms/{vm_id}/status",
                timeout=30
            )
            
            if response.status_code == 200:
                vm_status = response.json()
                status = vm_status.get("status", "unknown")
                
                if status in ["running", "ready"]:
                    return True
                elif status in ["failed", "error"]:
                    pytest.skip(f"VM failed to start: {vm_status}")
                    
        except Exception:
            pass
        
        time.sleep(15)
    
    pytest.skip(f"VM {vm_id} not ready within {timeout} seconds")


# Pytest configuration for real integration tests
def pytest_configure(config):
    """Configure pytest for real integration tests."""
    config.addinivalue_line(
        "markers", "real_integration: mark test as requiring real VirtualBox VMs"
    )
    config.addinivalue_line(
        "markers", "grpc: mark test as requiring gRPC communication"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow (may take several minutes)"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection for real integration tests."""
    for item in items:
        # Add slow marker to all real integration tests
        if "real_integration" in item.keywords:
            item.add_marker(pytest.mark.slow)
        
        # Add grpc marker to tests using gRPC
        if "grpc" in item.keywords:
            item.add_marker(pytest.mark.grpc)


# Skip all tests if real integration is not available
def pytest_runtest_setup(item):
    """Setup for each test run."""
    if "real_integration" in item.keywords:
        # Check if gRPC service is available
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(("localhost", 40000))
            sock.close()
            
            if result != 0:
                pytest.skip("gRPC service not available for real integration tests")
        except Exception:
            pytest.skip("Cannot check gRPC service availability")
