#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 10 VM + 10 Binary End-to-End Test

This script tests the complete end-to-end workflow with:
- 10 VirtualBox VMs queued and ready
- 10 test binaries processed through the complete workflow
- Real gRPC integration (no simulation)
- Complete report generation from actual VM execution data

If anything fails, the script stops and provides detailed error information.

Usage:
    python scripts/test-10vm-10binary-e2e.py
"""

import asyncio
import json
import os
import sys
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Any

import requests
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configuration
API_BASE_URL = "http://localhost:8000"
GRPC_ENDPOINT = "localhost:40000"
VM_TEMPLATE = "StefanScherer/windows_2019"
VM_PROVIDER = "virtualbox"
TEST_TIMEOUT = 3600  # 1 hour total timeout

# Test binaries (small Windows executables for testing)
TEST_BINARIES = [
    {
        "name": "test-calc",
        "content": b"""@echo off
echo TurdParty Test Calculator > C:\\TurdParty\\calc-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\calc-test-result.txt
calc.exe
echo Calculator test completed >> C:\\TurdParty\\calc-test-result.txt
""",
        "filename": "test-calc.bat"
    },
    {
        "name": "test-notepad",
        "content": b"""@echo off
echo TurdParty Test Notepad > C:\\TurdParty\\notepad-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\notepad-test-result.txt
notepad.exe C:\\TurdParty\\notepad-test-result.txt
echo Notepad test completed >> C:\\TurdParty\\notepad-test-result.txt
""",
        "filename": "test-notepad.bat"
    },
    {
        "name": "test-registry",
        "content": b"""@echo off
echo TurdParty Test Registry > C:\\TurdParty\\registry-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\registry-test-result.txt
reg add "HKCU\\Software\\TurdParty\\E2ETest" /v "TestRun" /t REG_SZ /d "%date%_%time%" /f
reg query "HKCU\\Software\\TurdParty\\E2ETest" >> C:\\TurdParty\\registry-test-result.txt
echo Registry test completed >> C:\\TurdParty\\registry-test-result.txt
""",
        "filename": "test-registry.bat"
    },
    {
        "name": "test-filesystem",
        "content": b"""@echo off
echo TurdParty Test Filesystem > C:\\TurdParty\\filesystem-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\filesystem-test-result.txt
mkdir C:\\TurdParty\\TestDir
echo Test file 1 > C:\\TurdParty\\TestDir\\test1.txt
echo Test file 2 > C:\\TurdParty\\TestDir\\test2.txt
dir C:\\TurdParty\\TestDir >> C:\\TurdParty\\filesystem-test-result.txt
echo Filesystem test completed >> C:\\TurdParty\\filesystem-test-result.txt
""",
        "filename": "test-filesystem.bat"
    },
    {
        "name": "test-processes",
        "content": b"""@echo off
echo TurdParty Test Processes > C:\\TurdParty\\processes-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\processes-test-result.txt
tasklist | findstr "explorer.exe" >> C:\\TurdParty\\processes-test-result.txt
tasklist | findstr "winlogon.exe" >> C:\\TurdParty\\processes-test-result.txt
echo Processes test completed >> C:\\TurdParty\\processes-test-result.txt
""",
        "filename": "test-processes.bat"
    },
    {
        "name": "test-services",
        "content": b"""@echo off
echo TurdParty Test Services > C:\\TurdParty\\services-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\services-test-result.txt
sc query Spooler >> C:\\TurdParty\\services-test-result.txt
sc query Themes >> C:\\TurdParty\\services-test-result.txt
echo Services test completed >> C:\\TurdParty\\services-test-result.txt
""",
        "filename": "test-services.bat"
    },
    {
        "name": "test-network",
        "content": b"""@echo off
echo TurdParty Test Network > C:\\TurdParty\\network-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\network-test-result.txt
ipconfig /all >> C:\\TurdParty\\network-test-result.txt
netstat -an | findstr "LISTENING" >> C:\\TurdParty\\network-test-result.txt
echo Network test completed >> C:\\TurdParty\\network-test-result.txt
""",
        "filename": "test-network.bat"
    },
    {
        "name": "test-environment",
        "content": b"""@echo off
echo TurdParty Test Environment > C:\\TurdParty\\environment-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\environment-test-result.txt
set >> C:\\TurdParty\\environment-test-result.txt
echo Environment test completed >> C:\\TurdParty\\environment-test-result.txt
""",
        "filename": "test-environment.bat"
    },
    {
        "name": "test-system-info",
        "content": b"""@echo off
echo TurdParty Test System Info > C:\\TurdParty\\systeminfo-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\systeminfo-test-result.txt
systeminfo >> C:\\TurdParty\\systeminfo-test-result.txt
echo System info test completed >> C:\\TurdParty\\systeminfo-test-result.txt
""",
        "filename": "test-systeminfo.bat"
    },
    {
        "name": "test-comprehensive",
        "content": b"""@echo off
echo TurdParty Comprehensive Test > C:\\TurdParty\\comprehensive-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\comprehensive-test-result.txt
echo Creating comprehensive test data...
mkdir C:\\TurdParty\\ComprehensiveTest
echo Test data 1 > C:\\TurdParty\\ComprehensiveTest\\data1.txt
echo Test data 2 > C:\\TurdParty\\ComprehensiveTest\\data2.txt
reg add "HKCU\\Software\\TurdParty\\ComprehensiveTest" /v "TestComplete" /t REG_SZ /d "YES" /f
tasklist | findstr "explorer" >> C:\\TurdParty\\comprehensive-test-result.txt
ipconfig >> C:\\TurdParty\\comprehensive-test-result.txt
echo Comprehensive test completed successfully! >> C:\\TurdParty\\comprehensive-test-result.txt
""",
        "filename": "test-comprehensive.bat"
    }
]


class TenVMTenBinaryE2ETester:
    """End-to-end tester for 10 VMs and 10 binaries."""

    def __init__(self):
        self.console = Console()
        self.created_vms = []
        self.uploaded_files = []
        self.injection_results = []
        self.report_results = []
        self.failed_operations = []

    def print_header(self):
        """Print test header."""
        header = Panel(
            "🖥️ [bold blue]10 VM + 10 Binary End-to-End Test[/bold blue] 🖥️\n\n"
            "This test validates the complete TurdParty workflow:\n"
            "• 10 VirtualBox VMs queued and ready\n"
            "• 10 test binaries processed through complete workflow\n"
            "• Real gRPC integration (no simulation)\n"
            "• Complete report generation from actual VM execution\n\n"
            "🔗 gRPC Endpoint: localhost:40000\n"
            "📊 Expected: 10 successful end-to-end workflows",
            title="💩🎉 TurdParty E2E Test 🎉💩",
            border_style="blue",
        )
        self.console.print(header)

    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        self.console.print("[bold blue]🔍 Checking Prerequisites...[/bold blue]")
        
        # Check API availability
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code != 200:
                self.console.print(f"[red]❌ API not available: {response.status_code}[/red]")
                return False
            self.console.print("[green]✅ API available[/green]")
        except Exception as e:
            self.console.print(f"[red]❌ API connection failed: {e}[/red]")
            return False

        # Check gRPC service
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(("localhost", 40000))
            sock.close()
            
            if result != 0:
                self.console.print(f"[red]❌ gRPC service not available on port 40000[/red]")
                return False
            self.console.print("[green]✅ gRPC service available[/green]")
        except Exception as e:
            self.console.print(f"[red]❌ gRPC check failed: {e}[/red]")
            return False

        return True

    def create_vm_pool(self) -> bool:
        """Create 10 VirtualBox VMs for the test."""
        self.console.print("[bold blue]🖥️ Creating 10 VirtualBox VMs...[/bold blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("Creating VMs...", total=10)
            
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = []
                
                for i in range(10):
                    vm_config = {
                        "name": f"e2e-test-vm-{i+1:02d}-{uuid.uuid4().hex[:8]}",
                        "template": VM_TEMPLATE,
                        "vm_type": "vagrant",
                        "provider": VM_PROVIDER,
                        "memory_mb": 4096,
                        "cpus": 2,
                        "disk_gb": 40,
                        "description": f"E2E Test VM {i+1}/10",
                        "tags": ["e2e-test", "virtualbox", "grpc"],
                    }
                    
                    future = executor.submit(self._create_single_vm, vm_config, i+1)
                    futures.append(future)
                
                for future in as_completed(futures):
                    try:
                        vm_id = future.result()
                        if vm_id:
                            self.created_vms.append(vm_id)
                            progress.update(task, advance=1, description=f"Created {len(self.created_vms)}/10 VMs")
                        else:
                            self.failed_operations.append("VM creation failed")
                            progress.update(task, advance=1, description=f"Failed VM creation")
                    except Exception as e:
                        self.failed_operations.append(f"VM creation error: {e}")
                        progress.update(task, advance=1, description=f"VM creation error")
        
        success_count = len(self.created_vms)
        self.console.print(f"[green]✅ Created {success_count}/10 VMs successfully[/green]")
        
        if success_count < 10:
            self.console.print(f"[red]❌ Only {success_count}/10 VMs created successfully[/red]")
            return False
        
        return True

    def _create_single_vm(self, vm_config: Dict[str, Any], vm_number: int) -> str:
        """Create a single VM."""
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v1/vms/",
                json=vm_config,
                timeout=120
            )
            
            if response.status_code in [200, 201]:
                vm_info = response.json()
                vm_id = vm_info.get("vm_id")
                return vm_id
            else:
                return None
                
        except Exception as e:
            return None

    def upload_test_binaries(self) -> bool:
        """Upload all 10 test binaries."""
        self.console.print("[bold blue]📁 Uploading 10 Test Binaries...[/bold blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("Uploading binaries...", total=10)
            
            for i, binary in enumerate(TEST_BINARIES):
                try:
                    files = {
                        'file': (binary["filename"], binary["content"], 'application/octet-stream')
                    }
                    
                    response = requests.post(
                        f"{API_BASE_URL}/api/v1/files/upload",
                        files=files,
                        timeout=60
                    )
                    
                    if response.status_code in [200, 201]:
                        file_info = response.json()
                        file_id = file_info.get("file_id")
                        self.uploaded_files.append({
                            "file_id": file_id,
                            "name": binary["name"],
                            "filename": binary["filename"]
                        })
                        progress.update(task, advance=1, description=f"Uploaded {len(self.uploaded_files)}/10 files")
                    else:
                        self.failed_operations.append(f"File upload failed: {binary['name']}")
                        progress.update(task, advance=1, description=f"Upload failed: {binary['name']}")
                        
                except Exception as e:
                    self.failed_operations.append(f"File upload error: {binary['name']} - {e}")
                    progress.update(task, advance=1, description=f"Upload error: {binary['name']}")
        
        success_count = len(self.uploaded_files)
        self.console.print(f"[green]✅ Uploaded {success_count}/10 files successfully[/green]")
        
        if success_count < 10:
            self.console.print(f"[red]❌ Only {success_count}/10 files uploaded successfully[/red]")
            return False
        
        return True

    def wait_for_vms_ready(self) -> bool:
        """Wait for all VMs to be ready."""
        self.console.print("[bold blue]⏳ Waiting for VMs to be ready...[/bold blue]")
        
        start_time = time.time()
        timeout = 1800  # 30 minutes for VirtualBox VMs
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("Waiting for VMs...", total=None)
            
            while time.time() - start_time < timeout:
                ready_count = 0
                
                for vm_id in self.created_vms:
                    try:
                        response = requests.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}/status", timeout=30)
                        if response.status_code == 200:
                            vm_status = response.json()
                            status = vm_status.get("status", "unknown")
                            if status in ["running", "ready"]:
                                ready_count += 1
                    except Exception:
                        pass
                
                progress.update(task, description=f"{ready_count}/10 VMs ready")
                
                if ready_count == 10:
                    self.console.print("[green]✅ All 10 VMs are ready![/green]")
                    return True
                
                time.sleep(30)  # Check every 30 seconds
            
            self.console.print(f"[red]❌ Timeout waiting for VMs to be ready ({ready_count}/10 ready)[/red]")
            return False

    def run_e2e_workflows(self) -> bool:
        """Run end-to-end workflows for all VM/binary combinations."""
        self.console.print("[bold blue]🚀 Running 10 End-to-End Workflows...[/bold blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("Running workflows...", total=10)
            
            with ThreadPoolExecutor(max_workers=3) as executor:  # Limit concurrency for VirtualBox
                futures = []
                
                for i in range(10):
                    vm_id = self.created_vms[i]
                    file_data = self.uploaded_files[i]
                    
                    future = executor.submit(self._run_single_e2e_workflow, vm_id, file_data, i+1)
                    futures.append(future)
                
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        if result["success"]:
                            self.injection_results.append(result)
                            progress.update(task, advance=1, description=f"Completed {len(self.injection_results)}/10 workflows")
                        else:
                            self.failed_operations.append(f"E2E workflow failed: {result.get('error', 'Unknown')}")
                            progress.update(task, advance=1, description=f"Failed workflow")
                    except Exception as e:
                        self.failed_operations.append(f"E2E workflow error: {e}")
                        progress.update(task, advance=1, description=f"Workflow error")
        
        success_count = len(self.injection_results)
        self.console.print(f"[green]✅ Completed {success_count}/10 workflows successfully[/green]")
        
        if success_count < 10:
            self.console.print(f"[red]❌ Only {success_count}/10 workflows completed successfully[/red]")
            return False
        
        return True

    def _run_single_e2e_workflow(self, vm_id: str, file_data: Dict[str, Any], workflow_number: int) -> Dict[str, Any]:
        """Run a single end-to-end workflow."""
        try:
            # Step 1: Inject file via gRPC
            injection_config = {
                "file_id": file_data["file_id"],
                "injection_path": f"C:\\TurdParty\\{file_data['filename']}",
                "execute_after_injection": True,
                "monitor": True,
                "permissions": "0755",
                "metadata": {
                    "workflow_number": workflow_number,
                    "test_type": "e2e",
                    "vm_provider": "virtualbox_grpc"
                }
            }
            
            response = requests.post(
                f"{API_BASE_URL}/api/v1/vms/{vm_id}/inject",
                json=injection_config,
                timeout=120
            )
            
            if response.status_code not in [200, 201]:
                return {"success": False, "error": f"Injection failed: {response.status_code}"}
            
            injection_info = response.json()
            injection_id = injection_info.get("injection_id")
            
            # Step 2: Wait for execution to complete
            if not self._wait_for_injection_complete(injection_id):
                return {"success": False, "error": "Injection timeout"}
            
            # Step 3: Generate report
            report_success = self._generate_workflow_report(file_data["file_id"])
            
            return {
                "success": True,
                "vm_id": vm_id,
                "file_id": file_data["file_id"],
                "injection_id": injection_id,
                "report_generated": report_success,
                "workflow_number": workflow_number
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _wait_for_injection_complete(self, injection_id: str) -> bool:
        """Wait for injection to complete."""
        start_time = time.time()
        timeout = 600  # 10 minutes per injection
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(
                    f"{API_BASE_URL}/api/v1/file_injection/{injection_id}/status",
                    timeout=30
                )
                
                if response.status_code == 200:
                    status_info = response.json()
                    status = status_info.get("status", "unknown")
                    
                    if status == "COMPLETED":
                        return True
                    elif status in ["FAILED", "ERROR"]:
                        return False
                        
            except Exception:
                pass
            
            time.sleep(15)
        
        return False

    def _generate_workflow_report(self, file_id: str) -> bool:
        """Generate report for workflow."""
        try:
            # Wait for ECS events to be indexed
            time.sleep(60)
            
            # Use the real report generation script
            import subprocess
            
            result = subprocess.run([
                sys.executable, 
                str(project_root / "scripts" / "generate-generic-report.py"), 
                file_id
            ], capture_output=True, text=True, timeout=300, cwd=project_root)
            
            return result.returncode == 0
            
        except Exception:
            return False

    def cleanup_resources(self):
        """Clean up all test resources."""
        self.console.print("[bold blue]🧹 Cleaning up test resources...[/bold blue]")
        
        # Clean up VMs
        for vm_id in self.created_vms:
            try:
                response = requests.delete(f"{API_BASE_URL}/api/v1/vms/{vm_id}", timeout=120)
                if response.status_code in [200, 204]:
                    self.console.print(f"[green]✅ Cleaned up VM: {vm_id}[/green]")
                else:
                    self.console.print(f"[yellow]⚠️ VM cleanup warning: {vm_id}[/yellow]")
            except Exception as e:
                self.console.print(f"[yellow]⚠️ VM cleanup error: {e}[/yellow]")
        
        # Clean up files
        for file_data in self.uploaded_files:
            try:
                file_id = file_data["file_id"]
                response = requests.delete(f"{API_BASE_URL}/api/v1/files/{file_id}", timeout=30)
                if response.status_code in [200, 204]:
                    self.console.print(f"[green]✅ Cleaned up file: {file_id}[/green]")
                else:
                    self.console.print(f"[yellow]⚠️ File cleanup warning: {file_id}[/yellow]")
            except Exception as e:
                self.console.print(f"[yellow]⚠️ File cleanup error: {e}[/yellow]")

    def print_final_results(self):
        """Print final test results."""
        total_workflows = 10
        successful_workflows = len(self.injection_results)
        failed_workflows = len(self.failed_operations)
        
        # Create results table
        table = Table(title="💩🎉 TurdParty E2E Test Results 🎉💩")
        table.add_column("Metric", style="cyan")
        table.add_column("Count", style="magenta")
        table.add_column("Status", style="green")
        
        table.add_row("Total Workflows", str(total_workflows), "📊")
        table.add_row("Successful Workflows", str(successful_workflows), "✅" if successful_workflows == total_workflows else "⚠️")
        table.add_row("Failed Operations", str(failed_workflows), "❌" if failed_workflows > 0 else "✅")
        table.add_row("VMs Created", str(len(self.created_vms)), "🖥️")
        table.add_row("Files Uploaded", str(len(self.uploaded_files)), "📁")
        
        self.console.print(table)
        
        if self.failed_operations:
            self.console.print("\n[red]❌ Failed Operations:[/red]")
            for i, failure in enumerate(self.failed_operations, 1):
                self.console.print(f"  {i}. {failure}")

    def run_complete_test(self) -> bool:
        """Run the complete 10 VM + 10 Binary E2E test."""
        start_time = time.time()
        
        self.print_header()
        
        try:
            # Step 1: Check prerequisites
            if not self.check_prerequisites():
                self.console.print("[red]❌ Prerequisites not met[/red]")
                return False
            
            # Step 2: Create VM pool
            if not self.create_vm_pool():
                self.console.print("[red]❌ Failed to create VM pool[/red]")
                return False
            
            # Step 3: Upload test binaries
            if not self.upload_test_binaries():
                self.console.print("[red]❌ Failed to upload test binaries[/red]")
                return False
            
            # Step 4: Wait for VMs to be ready
            if not self.wait_for_vms_ready():
                self.console.print("[red]❌ VMs not ready in time[/red]")
                return False
            
            # Step 5: Run E2E workflows
            if not self.run_e2e_workflows():
                self.console.print("[red]❌ E2E workflows failed[/red]")
                return False
            
            # Success!
            total_time = time.time() - start_time
            
            success_panel = Panel(
                f"🎉 [bold green]10 VM + 10 Binary E2E Test Completed![/bold green] 🎉\n\n"
                f"⏱️ Total Time: {total_time:.2f} seconds ({total_time/60:.1f} minutes)\n"
                f"🖥️ VMs Created: {len(self.created_vms)}\n"
                f"📁 Files Uploaded: {len(self.uploaded_files)}\n"
                f"🚀 Workflows Completed: {len(self.injection_results)}\n"
                f"📊 Reports Generated: {len([r for r in self.injection_results if r.get('report_generated')])}\n"
                f"🔗 gRPC Endpoint: {GRPC_ENDPOINT}\n\n"
                f"✅ All 10 end-to-end workflows completed successfully!",
                title="🖥️ E2E Test Complete",
                border_style="green",
            )
            self.console.print(success_panel)
            
            return True
            
        except Exception as e:
            error_msg = f"E2E test failed: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return False
        finally:
            self.print_final_results()
            self.cleanup_resources()


def main():
    """Main function to run 10 VM + 10 Binary E2E test."""
    tester = TenVMTenBinaryE2ETester()
    
    try:
        success = tester.run_complete_test()
        
        if success:
            print(f"\n🎉 10 VM + 10 Binary E2E test passed!")
            return 0
        else:
            print(f"\n❌ 10 VM + 10 Binary E2E test failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        tester.cleanup_resources()
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        tester.cleanup_resources()
        return 1


if __name__ == "__main__":
    exit(main())
